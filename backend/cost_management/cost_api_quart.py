"""Cost analytics API routes for Quart."""

from quart import Blueprint, jsonify, request, current_app
from typing import List, Optional, Dict, Any
import logging
from backend.auth.auth_utils import get_authenticated_user_details
from backend.cost_management.cost_management_service import CostManagementService
from backend.cost_management.cost_allocation_service import CostAllocationService

# Create blueprint
cost_bp = Blueprint("cost", __name__, url_prefix="/api/cost")
logger = logging.getLogger(__name__)

# Initialize services
cost_service = CostManagementService()
allocation_service = CostAllocationService()

# Start periodic cost collection when the Quart app begins serving requests
from backend.cost_management.cost_collection_task import (
    schedule_cost_collection,
    run_cost_collection_task,
)

# Import for Cosmos DB access
import os
from azure.cosmos.aio import CosmosClient
from datetime import datetime, timezone, timedelta

@cost_bp.before_app_serving
async def start_cost_collection() -> None:
    """Kick off the hourly cost collection scheduler."""
    schedule_cost_collection()


async def _resolve_user_and_role(headers) -> tuple[Dict[str, Any], Optional[str], Optional[str], Any]:
    """Return the authenticated user and resolved role, consulting the database if available."""
    user = await get_authenticated_user_details(headers)
    user_id = user.get("id") or user.get("user_principal_id") if user else None

    role_value = None
    if user:
        role_value = user.get("role")
        if not role_value:
            roles = user.get("roles", [])
            role_value = roles[0] if roles else None

    rbac_client = current_app.cosmos_conversation_client
    if rbac_client and user_id:
        try:
            db_user = await rbac_client.get_user(user_id)
            if db_user and db_user.get("role"):
                role_value = db_user.get("role")
        except Exception as e:  # pragma: no cover - best effort logging
            logger.warning(f"Failed to get user from database: {e}")

    user_role = role_value.lower() if isinstance(role_value, str) else None
    return user, user_role, user_id, rbac_client


async def get_latest_cost_data_from_cosmos(time_range: str = "month", project_id: Optional[str] = None):
    """Retrieve cost data from Cosmos DB for the specified time range.
    
    Args:
        time_range: Time period to query (e.g., "7d", "30d", "month", "quarter", "year")
        project_id: Optional project ID to filter by
        
    Returns:
        Tuple of (list of cost documents, warning dict or None)
    """
    try:
        # Initialize Cosmos client
        cosmos_endpoint = os.environ.get("COSMOSDB_ENDPOINT")
        cosmos_key = os.environ.get("COSMOSDB_KEY") or os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
        cosmos_database = os.environ.get("COSMOSDB_DATABASE") or os.environ.get("AZURE_COSMOSDB_DATABASE")
        
        if not all([cosmos_endpoint, cosmos_key, cosmos_database]):
            logger.warning("Cosmos DB configuration missing")
            return None
            
        async with CosmosClient(cosmos_endpoint, credential=cosmos_key) as cosmos_client:
            database = cosmos_client.get_database_client(cosmos_database)
            cost_container = database.get_container_client("cost_data")
            
            # Calculate date range based on time_range parameter
            end_date = datetime.now(timezone.utc)
            if time_range == "week" or time_range == "7d":
                start_date = end_date - timedelta(days=7)
            elif time_range == "month" or time_range == "30d":
                start_date = end_date - timedelta(days=30)
            elif time_range == "quarter":
                start_date = end_date - timedelta(days=90)
            elif time_range == "year":
                start_date = end_date - timedelta(days=365)
            else:
                # Default to last month
                start_date = end_date - timedelta(days=30)
            
            # Format dates for query
            start_date_str = start_date.isoformat()
            end_date_str = end_date.isoformat()
            
            # Query for cost data within the date range
            query = """
                SELECT * FROM c 
                WHERE c.id LIKE 'cost-%' 
                AND c.timestamp >= @start_date 
                AND c.timestamp <= @end_date
                ORDER BY c.timestamp DESC
            """
            
            query_params = [
                {"name": "@start_date", "value": start_date_str},
                {"name": "@end_date", "value": end_date_str}
            ]
            
            items = []
            async for item in cost_container.query_items(
                query=query,
                parameters=query_params,
                partition_key=None,  # Cross-partition query
                max_item_count=100  # Get up to 100 documents
            ):
                items.append(item)
            
            if not items:
                logger.info(f"No cost data found in Cosmos DB for time range: {time_range}")
                return None
            
            logger.info(f"Retrieved {len(items)} cost documents from Cosmos DB for time range: {time_range}")
            
            # Check if most recent data is stale
            latest_data = items[0]  # Already sorted by timestamp DESC
            timestamp_str = latest_data.get("timestamp")
            warning = None
            
            if timestamp_str:
                if timestamp_str.endswith('Z'):
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                elif '+' in timestamp_str or timestamp_str.endswith('00:00'):
                    timestamp = datetime.fromisoformat(timestamp_str)
                else:
                    timestamp = datetime.fromisoformat(timestamp_str).replace(tzinfo=timezone.utc)
                
                age = datetime.now(timezone.utc) - timestamp
                hours_old = age.total_seconds() / 3600
                
                # Add warning if data is stale
                if hours_old > 24:
                    warning = {
                        "message": f"Most recent cost data is {hours_old:.1f} hours old. Consider collecting fresh data.",
                        "severity": "error",
                        "lastUpdate": timestamp_str,
                        "hoursOld": hours_old
                    }
                elif hours_old > 4:
                    warning = {
                        "message": f"Most recent cost data is {hours_old:.1f} hours old.",
                        "severity": "warning",
                        "lastUpdate": timestamp_str,
                        "hoursOld": hours_old
                    }
            
            return items, warning
            
    except Exception as e:
        logger.error(f"Error retrieving cost data from Cosmos DB: {e}")
        return None


@cost_bp.route("/overview", methods=["GET"])
async def get_cost_overview():
    """Return basic cost overview."""
    try:
        user, user_role, user_id, rbac_client = await _resolve_user_and_role(request.headers)

        if not user or user_role == "regular_user":
            return jsonify({"error": "Not authorized"}), 403

        # Get query parameters
        time_range = request.args.get("timeRange", "month")
        region_id = request.args.get("regionId")
        project_id = request.args.get("projectId")

        if not rbac_client:
            return jsonify({"error": "Database service unavailable"}), 503

        # Check access permissions and get accessible projects
        accessible_projects = await rbac_client.get_accessible_projects(user_id)
        accessible_map = {p["id"]: p.get("region") for p in accessible_projects}

        if user_role != "super_admin":
            if project_id and project_id not in accessible_map:
                return jsonify({"error": "Project not accessible"}), 403

            if user_role == "regional_admin":
                user_region = user.get("region")
                if region_id and region_id != user_region:
                    return jsonify({"error": "Region not accessible"}), 403
                region_id = user_region

        # Filter accessible projects by region if specified
        if region_id and region_id != 'all':
            accessible_projects = [p for p in accessible_projects if p.get("region") == region_id]
            accessible_map = {p["id"]: p.get("region") for p in accessible_projects}

        # Try to get cost data from Cosmos DB first
        project_costs = []
        total = 0.0
        warning = None
        use_cosmos_data = False
        result = None  # Initialize result variable

        # TEMPORARY: Skip Cosmos DB and use mock data for debugging
        logger.info("TEMPORARY: Skipping Cosmos DB and using mock data for debugging")
        cosmos_result = None
        if False and cosmos_result:
            if isinstance(cosmos_result, tuple):
                cosmos_documents, warning = cosmos_result
            else:
                cosmos_documents = cosmos_result
                warning = None
            
            if cosmos_documents and isinstance(cosmos_documents, list) and len(cosmos_documents) > 0:
                logger.info(f"Using {len(cosmos_documents)} cost documents from Cosmos DB")
                use_cosmos_data = True
                
                # Aggregate costs from all documents
                project_cost_map = {}  # projectId -> {cost: total, latest_doc_index: index}
                all_project_ids = set()
                
                # First pass: aggregate costs from all documents
                for doc_index, cosmos_doc in enumerate(cosmos_documents):
                    stored_project_costs = cosmos_doc.get("projectCosts", [])
                    
                    for stored_cost in stored_project_costs:
                        pid = stored_cost["projectId"]
                        cost = stored_cost["cost"]
                        
                        # Skip if filtering by project and this isn't the one
                        if project_id and pid != project_id:
                            continue
                        
                        all_project_ids.add(pid)
                        
                        if pid in project_cost_map:
                            # Add to existing cost
                            project_cost_map[pid]["cost"] += cost
                        else:
                            # First time seeing this project
                            project_cost_map[pid] = {
                                "cost": cost,
                                "latest_doc_index": doc_index  # Track which doc has latest data
                            }
                
                # Get project names and details from RBAC
                if all_project_ids:
                    logger.debug(f"About to call get_project_names_map with {len(all_project_ids)} project IDs")
                    try:
                        project_names_map = await cost_service.get_project_names_map(rbac_client, list(all_project_ids))
                        logger.debug(f"get_project_names_map completed successfully")
                    except Exception as e:
                        logger.error(f"Error in get_project_names_map: {str(e)}")
                        import traceback
                        logger.error(f"Traceback: {traceback.format_exc()}")
                        project_names_map = {}
                else:
                    project_names_map = {}
                
                # Second pass: build final project costs with aggregated data
                for pid, cost_data in project_cost_map.items():
                    # Get project details from accessible projects
                    project_details = None
                    for proj in accessible_projects:
                        if proj["id"] == pid:
                            project_details = proj
                            break
                    
                    if not project_details:
                        # Skip projects user doesn't have access to
                        continue
                    
                    # Filter by region if specified
                    if region_id and region_id != 'all' and project_details.get("region") != region_id:
                        continue
                    
                    project_name = project_names_map.get(pid, project_details.get("name", f"Project {pid}"))
                    budget = project_details.get("cost_limit") if project_details.get("cost_limit") is not None else 5000.0
                    region = project_details.get("region")
                    
                    project_costs.append({
                        "project": project_name,
                        "projectId": pid,
                        "cost": cost_data["cost"],  # Aggregated cost
                        "budget": budget,
                        "region": region,
                        "regionId": region,
                    })
                    total += cost_data["cost"]

        # If no data from Cosmos or it's too old, fall back to Azure Cost Management
        if not use_cosmos_data:
            try:
                logger.info(f"Querying Azure Cost Management for tag 'project-id' with project_id: {project_id}")
                logger.info(f"Time range: {time_range}")
                result = await cost_service.query_cost_by_tag(
                    time_range, "project-id", project_id
                )
                logger.info(f"Azure Cost Management query completed")
                logger.info(f"Result type: {type(result)}")
                if result:
                    logger.info(f"Result has rows: {hasattr(result, 'rows')}")
                    if hasattr(result, 'rows'):
                        logger.info(f"Number of rows: {len(getattr(result, 'rows', []))}")

                # Handle successful response
                if result and hasattr(result, 'rows'):
                    rows = getattr(result, "rows", [])
                    project_ids = []
                    project_cost_data = []

                    # First pass: collect project IDs and cost data
                    for row in rows:
                        try:
                            # Azure Cost Management response format when grouping by TagKey
                            # Format: [cost, tagKey, tagValue, currency]
                            # Example: [41.56, 'project-id', '35a7db33-b409-4873-b3bc-54e42931bd3d', 'EUR']
                            if len(row) >= 4:
                                project_cost = float(row[0])  # Cost is first column
                                tag_key = row[1]  # Should be 'project-id'
                                pid = str(row[2]) if row[2] else None  # Project ID
                                currency = row[3]  # Currency
                                
                                # Skip rows with None project ID (untagged resources)
                                if not pid or pid == "None":
                                    logger.debug(f"Skipping untagged resource with cost {project_cost}")
                                    continue
                                    
                                logger.debug(f"Processing project {pid} with cost {project_cost} {currency}")
                            else:
                                logger.warning(f"Unexpected row format: {row}")
                                continue

                            # No user-based filtering - use application rights
                            region = None  # Will be fetched from Cosmos DB

                            project_ids.append(pid)
                            project_cost_data.append({
                                'id': pid,
                                'cost': project_cost,
                                'region': region
                            })
                            total += project_cost
                        except (IndexError, ValueError, TypeError) as row_error:
                            logger.warning(f"Failed to parse cost data row {row}: {str(row_error)}")
                            continue

                    # Fetch project names from Cosmos DB
                    logger.info(f"Fetching names for {len(project_ids)} projects: {project_ids}")
                    project_names_map = await cost_service.get_project_names_map(rbac_client, project_ids)
                    logger.info(f"Retrieved project names: {project_names_map}")

                    # Second pass: create project cost objects with names and real budgets
                    for data in project_cost_data:
                        pid = data['id']
                        project_name = project_names_map.get(pid, pid)  # Fallback to ID if name not found

                        # Get the real budget (cost_limit) from the project data
                        project_budget = None
                        for project in accessible_projects:
                            if project.get("id") == pid:
                                project_budget = project.get("cost_limit")
                                break

                        # Use real budget if available, otherwise use a default
                        budget = project_budget if project_budget is not None else 5000.0  # Default budget in EUR

                        project_costs.append(
                            {
                                "project": project_name,
                                "projectId": pid,
                                "cost": data['cost'],  # Cost is already in EUR from Azure Cost Management
                                "budget": budget,  # Real budget from project cost_limit
                                "region": data['region'],
                                "regionId": data['region'],
                            }
                        )
                elif result == []:
                    # Empty result from service (likely due to API error)
                    logger.info("Azure Cost Management service returned empty result, using mock data")
                    result = None

            except Exception as e:
                logger.warning(f"Failed to query Azure Cost Management: {str(e)}")
                result = None

        # Use mock data if no real data available
        logger.debug(f"project_costs length: {len(project_costs)}, result: {result}, use_cosmos_data: {use_cosmos_data}")
        if not project_costs and result is None:
            logger.info("Using mock cost data for development/fallback")
            if accessible_projects:
                # Get project names for mock data too
                project_ids = [p["id"] for p in accessible_projects]
                logger.info(f"Fetching names for mock data - {len(project_ids)} projects: {project_ids}")
                project_names_map = await cost_service.get_project_names_map(rbac_client, project_ids)
                logger.info(f"Retrieved project names for mock data: {project_names_map}")

                for project in accessible_projects:
                    pid = project["id"]
                    region = project.get("region")
                    project_name = project_names_map.get(pid, project.get("name", f"Project {pid}"))
                    mock_cost = 1500 + (hash(pid) % 3000)  # Mock cost in EUR

                    # Get the real budget (cost_limit) from the project data
                    project_budget = project.get("cost_limit")
                    budget = project_budget if project_budget is not None else 5000.0  # Default budget in EUR

                    project_costs.append(
                        {
                            "project": project_name,
                            "projectId": pid,
                            "cost": mock_cost,
                            "budget": budget,  # Real budget from project cost_limit
                            "region": region,
                            "regionId": region,
                        }
                    )
                total = sum(p["cost"] for p in project_costs)
            else:
                # Super admin mock data (costs in EUR) - use real budgets if available
                mock_projects = [
                    {"id": "1", "name": "Project A", "region": "West US 2", "regionId": "westus2", "cost": 2500},
                    {"id": "2", "name": "Project B", "region": "East US", "regionId": "eastus", "cost": 3200},
                    {"id": "3", "name": "Project C", "region": "West Europe", "regionId": "westeurope", "cost": 1800},
                ]

                project_costs = []
                for mock_project in mock_projects:
                    # Try to get real budget from accessible projects if available
                    project_budget = None
                    if accessible_projects:
                        for project in accessible_projects:
                            if project.get("id") == mock_project["id"]:
                                project_budget = project.get("cost_limit")
                                break

                    # Use real budget if available, otherwise use default
                    budget = project_budget if project_budget is not None else 5000.0  # Default budget in EUR

                    project_costs.append({
                        "project": mock_project["name"],
                        "projectId": mock_project["id"],
                        "cost": mock_project["cost"],  # EUR
                        "budget": budget,  # Real budget from project cost_limit or default
                        "region": mock_project["region"],
                        "regionId": mock_project["regionId"],
                    })
                total = sum(p["cost"] for p in project_costs)

        # Generate region costs based on Cosmos DB data or accessible projects
        region_costs = []
        
        # If we used Cosmos data, calculate region costs from aggregated project costs
        if use_cosmos_data and project_costs:
            # Calculate region costs from aggregated project costs
            region_totals = {}
            for project_cost in project_costs:
                region = project_cost.get("region")
                if region:
                    if region not in region_totals:
                        region_totals[region] = 0
                    region_totals[region] += project_cost["cost"]
            
            for region_id, cost in region_totals.items():
                # Get region name and budget from RBAC client
                region_name = region_id  # Default to ID
                region_budget = None
                try:
                    # Try to get region data including cost_limit from RBAC client
                    region_data = await rbac_client.get_region(region_id)
                    if region_data:
                        region_name = region_data.get("name", region_id)
                        region_budget = region_data.get("cost_limit")
                except Exception as e:
                    logger.warning(f"Could not get region data for {region_id}: {e}")

                # Use real budget if available, otherwise use a default
                budget = region_budget if region_budget is not None else 10000.0  # Default region budget in EUR

                region_costs.append({
                    "regionId": region_id,
                    "region": region_name,
                    "cost": cost,
                    "budget": budget  # Real budget from region cost_limit
                })
        else:
            # Calculate region costs from project costs
            if accessible_projects:
                region_totals = {}
                for project in accessible_projects:
                    region = project.get("region")
                    if region:
                        if region not in region_totals:
                            region_totals[region] = 0
                        # Find the project cost for this project
                        project_cost_data = next((p for p in project_costs if p["projectId"] == project["id"]), None)
                        if project_cost_data:
                            region_totals[region] += project_cost_data["cost"]

                for region_id, cost in region_totals.items():
                    # Get region name and budget from RBAC client
                    region_name = region_id  # Default to ID
                    region_budget = None
                    try:
                        # Try to get region data including cost_limit from RBAC client
                        region_data = await rbac_client.get_region(region_id)
                        if region_data:
                            region_name = region_data.get("name", region_id)
                            region_budget = region_data.get("cost_limit")
                    except Exception as e:
                        logger.warning(f"Could not get region data for {region_id}: {e}")

                    # Use real budget if available, otherwise use a default
                    budget = region_budget if region_budget is not None else 10000.0  # Default region budget in EUR

                    region_costs.append({
                        "regionId": region_id,
                        "region": region_name,
                        "cost": cost,
                        "budget": budget  # Real budget from region cost_limit
                    })

        # Generate mock service costs based on total cost
        service_costs = []
        if total > 0:
            service_costs = [
                {"service": "Azure Storage", "cost": total * 0.4, "isShared": False},
                {"service": "Azure Search", "cost": total * 0.3, "isShared": False},
                {"service": "Azure Functions", "cost": total * 0.2, "isShared": False},
                {"service": "Azure Cosmos DB", "cost": total * 0.1, "isShared": True},
            ]

        response_data = {
            "projectCosts": project_costs,
            "serviceCosts": service_costs,
            "regionCosts": region_costs,
            "totalCost": total,
        }
        
        # Add warning if cost data is stale
        if warning:
            response_data["warning"] = warning
            
        return jsonify(response_data)

    except Exception as e:
        import traceback
        logger.error(f"Error in get_cost_overview: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": "Internal server error"}), 500


@cost_bp.route("/resources", methods=["GET"])
async def get_resource_costs():
    """Get cost data for Azure resources."""
    try:
        user, user_role, user_id, rbac_client = await _resolve_user_and_role(request.headers)

        if not user or user_role == "regular_user":
            return jsonify({"error": "Not authorized"}), 403

        # Get query parameters
        time_range = request.args.get("timeRange", "month")
        region_id = request.args.get("regionId")
        project_id = request.args.get("projectId")
        resource_type = request.args.get("resourceType")

        if not rbac_client:
            return jsonify({"error": "Database service unavailable"}), 503

        # Check access permissions
        if user_role != "super_admin":
            projects = await rbac_client.get_accessible_projects(user_id)
            accessible_ids = {p["id"] for p in projects}
            if project_id and project_id not in accessible_ids:
                return jsonify({"error": "Project not accessible"}), 403
            if user_role == "regional_admin":
                user_region = user.get("region")
                if region_id and region_id != user_region:
                    return jsonify({"error": "Region not accessible"}), 403
                region_id = user_region

        # Query resource costs
        try:
            result = await cost_service.query_cost_by_resource(
                time_range, region_id, resource_type
            )
            resources = []
            total = 0.0
            if result:
                rows = getattr(result, "rows", [])
                for row in rows:
                    # Azure Cost Management response format: [cost, resource_id, currency]
                    cost = float(row[0])  # Cost is always first column
                    resource_name = row[1] if len(row) > 1 else "unknown"  # Resource ID in second column
                    # Extract just the resource name from the full resource ID
                    if '/' in resource_name:
                        resource_name = resource_name.split('/')[-1]

                    resources.append(
                        {
                            "name": resource_name,
                            "resourceId": row[1] if len(row) > 1 else "unknown",
                            "resourceType": resource_type or "",
                            "cost": cost,
                            "isShared": None,
                            "region": region_id,
                            "regionId": region_id,
                        }
                    )
                    total += cost
        except Exception as e:
            logger.warning(f"Failed to query Azure Cost Management: {str(e)}")
            # Return mock data for development (costs in EUR)
            resources = [
                {
                    "name": "storage-account-01",
                    "resourceId": "res-1",
                    "resourceType": "storage",
                    "cost": 850,  # EUR
                    "isShared": False,
                    "region": region_id,
                    "regionId": region_id,
                },
                {
                    "name": "search-service-01",
                    "resourceId": "res-2",
                    "resourceType": "search",
                    "cost": 1200,  # EUR
                    "isShared": False,
                    "region": region_id,
                    "regionId": region_id,
                },
                {
                    "name": "cosmos-db-01",
                    "resourceId": "res-3",
                    "resourceType": "database",
                    "cost": 950,  # EUR
                    "isShared": True,
                    "region": region_id,
                    "regionId": region_id,
                },
            ]
            if resource_type:
                resources = [r for r in resources if r["resourceType"] == resource_type]
            total = sum(r["cost"] for r in resources)

        return jsonify({"resources": resources, "totalCost": total})

    except Exception as e:
        logger.error(f"Error in get_resource_costs: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cost_bp.route("/containers", methods=["GET"])
async def get_container_costs():
    """Get container-level cost breakdown."""
    try:
        user, user_role, _, _ = await _resolve_user_and_role(request.headers)

        if not user or user_role == "regular_user":
            return jsonify({"error": "Not authorized"}), 403

        # Mock response for now
        return jsonify({"containers": [], "totalCost": 0.0})

    except Exception as e:
        logger.error(f"Error in get_container_costs: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cost_bp.route("/indexers", methods=["GET"])
async def get_indexer_costs():
    """Get indexer cost breakdown."""
    try:
        user, user_role, _, _ = await _resolve_user_and_role(request.headers)

        if not user or user_role == "regular_user":
            return jsonify({"error": "Not authorized"}), 403

        # Mock response for now
        return jsonify({"indexers": [], "totalCost": 0.0})

    except Exception as e:
        logger.error(f"Error in get_indexer_costs: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cost_bp.route("/trends", methods=["GET"])
async def get_cost_trends():
    """Get historical cost trends."""
    try:
        user, user_role, _, _ = await _resolve_user_and_role(request.headers)

        if not user or user_role == "regular_user":
            return jsonify({"error": "Not authorized"}), 403

        # Mock response for now
        return jsonify({"trends": [], "periods": []})

    except Exception as e:
        logger.error(f"Error in get_cost_trends: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cost_bp.route("/allocation", methods=["POST"])
async def update_cost_allocation():
    """Update project cost allocations."""
    try:
        user, user_role, _, _ = await _resolve_user_and_role(request.headers)

        if not user or user_role not in ["super_admin", "regional_admin"]:
            return jsonify({"error": "Not authorized"}), 403

        # Get request data
        data = await request.get_json()

        # Mock response for now
        return jsonify({"success": True, "message": "Cost allocation updated"})

    except Exception as e:
        logger.error(f"Error in update_cost_allocation: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cost_bp.route("/collect-now", methods=["POST"])
async def collect_cost_now():
    """Manually trigger cost data collection."""
    try:
        user, user_role, user_id, _ = await _resolve_user_and_role(request.headers)

        logger.info(f"Role for {user_id or 'unknown'} is '{user_role}'")

        if not user or user_role not in ["super_admin", "regional_admin"]:
            return jsonify({"error": "Not authorized"}), 403

        await run_cost_collection_task()
        return jsonify({"message": "Cost data collection triggered"})

    except Exception as e:  # pragma: no cover - safeguard
        logger.error(f"Error in collect_cost_now: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500
