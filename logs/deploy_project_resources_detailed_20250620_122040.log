2025-06-20 12:20:40,173 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_122040.log
2025-06-20 12:20:40,211 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:20:40,231 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:20:40,255 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:20:40,255 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:20:40,256 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:40,429 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:20:39 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:20:40,429 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:20:40,430 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:20:40,431 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:20:40,450 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:20:40,450 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:20:40,450 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:20:40,450 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:20:40,450 - root - INFO - Using DummyCredential for local development
2025-06-20 12:20:40,450 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:20:40,451 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:20:40,451 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:20:40,451 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:20:40,451 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:20:40,451 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:20:40,452 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:20:40,452 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
